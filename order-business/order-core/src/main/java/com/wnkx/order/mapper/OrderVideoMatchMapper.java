package com.wnkx.order.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.core.enums.OrderStatusEnum;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.system.api.domain.bo.biz.datastatistics.ModelOrderCommissionAnalysisBO;
import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.order.MyPreselectDockingListDTO;
import com.ruoyi.system.api.domain.dto.order.MyPreselectEndMatchListDTO;
import com.ruoyi.system.api.domain.dto.order.MyPreselectFailListDTO;
import com.ruoyi.system.api.domain.dto.order.OrderPoolListDTO;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO;
import com.wnkx.db.mapper.SuperMapper;
import com.wnkx.order.service.IOrderVideoService;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/15 15:55
 */
@Mapper
public interface OrderVideoMatchMapper extends SuperMapper<OrderVideoMatch> {

    /**
     * 通过视频订单ID查询列表
     */
    default List<OrderVideoMatch> selectListByVideoIds(Collection<Long> videoIds) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatch>()
                .in(OrderVideoMatch::getVideoId, videoIds)
        );
    }

    /**
     * 通过视频订单ID获取匹配单
     */
    default OrderVideoMatch getActiveByVideoId(Long videoId) {
        return selectOne(new LambdaQueryWrapper<OrderVideoMatch>()
                .eq(OrderVideoMatch::getVideoId, videoId)
                .orderByDesc(OrderVideoMatch::getStartTime)
                .last("limit 1")
        );
    }

    /**
     * 查询视频订单已淘汰模特数量
     */
    List<CountVideoOutModelVO> countVideoOutModelByVideoIds(@Param("videoIds") Collection<Long> videoIds);

    /**
     * 通过视频订单ID查询历史匹配单
     */
    default List<OrderVideoMatch> selectHistoryMatchListByVideoId(Long videoId) {
        return selectList(new LambdaQueryWrapper<OrderVideoMatch>()
                .eq(OrderVideoMatch::getVideoId, videoId)
                .orderByDesc(OrderVideoMatch::getStartTime)
        );
    }

    /**
     * 通过视频订单id查询活跃的关联的预选模特（运营端订单列表）
     */
    List<VideoMatchOrderVO> selectActiveListByVideoIds(@Param("videoIds") List<Long> videoIds);

    /**
     * 预选管理-模特匹配-订单池
     */
    List<OrderPoolListVO> selectOrderPoolListByCondition(@Param("dto") OrderPoolListDTO orderPoolListDTO);

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    List<MyPreselectDockingListVO> selectMyPreselectDockingList(@Param("dto") MyPreselectDockingListDTO dto);

    /**
     * 查询视频订单历史选择数据
     * @param videoIds
     * @return
     */
    List<HistoryPreselectModelCountVO> getHistoryPreselectModelCountList(@Param("videoIds") List<Long> videoIds);

    /**
     * 预选管理-模特匹配-我的预选-结束预选
     */
    List<MyPreselectEndMatchListVO> selectMyPreselectEndMatchList(@Param("dto") MyPreselectEndMatchListDTO dto);

    /**
     * 清空匹配单的标记
     */
    void clearFlag(@Param("matchIds") List<Long> matchIds);

    /**
     * 通过添加预选时间筛选符合条件的匹配单的videoId（给订单列表筛选用）
     */
    Set<Long> selectVideoIdByAddPreselectTimeOfOrderVideo(@Param("addPreselectTimeBegin") String addPreselectTimeBegin, @Param("addPreselectTimeEnd") String addPreselectTimeEnd);


    /**
     * 通过主携带视频订单ID查询被携带匹配单
     */
    List<OrderVideoMatch> selectCarryMatchListByMainCarryVideoId(@Param("mainCarryVideoId") Long mainCarryVideoId);

    /**
     * 获取模特需携带订单数
     *
     * @param modelIds 模特id
     */
    List<ModelCarryVO> getModelMainCarryCountByModelIds(@Param("modelIds") Collection<Long> modelIds);

    /**
     * 获取模特已携带订单数
     */
    List<ModelCarryVO> getModelCarriedCountByModelIds(@Param("modelIds") Collection<Long> modelIds);

    /**
     * 获取模特剩余需携带订单数
     */
    List<ModelCarryVO> getModelLeftCarryCountByModelIds(@Param("modelIds") Collection<Long> modelIds);

    /**
     * 标记订单-主携带订单下拉框
     */
    List<ModelCarryVO> markOrderMainCarryListByModelId(@Param("videoId") Long videoId, @Param("modelId") Long modelId);

    /**
     * 获取主携带视频订单的匹配单
     */
    OrderVideoMatch getMainCarryMatchByMainCarryVideoId(@Param("mainCarryVideoId") Long mainCarryVideoId);

    /**
     * 通过视频订单ID获取视频订单已携带订单数
     */
    Long getCarriedCountByMainCarryVideoId(@Param("mainCarryVideoId") Long mainCarryVideoId, @Param("ignoreCarryVideoId") Long ignoreCarryVideoId);

    /**
     * 更新匹配单 若字段为null 更新为null
     * PS:请注意字段值
     */
    void updateOrderVideoMatchFieldNullToNull(@Param("orderVideoMatch") OrderVideoMatch orderVideoMatch);

    /**
     * 获取不同于视频订单carry_ignore的匹配单
     */
    List<OrderVideoMatch> getNotCarryIgnoreMatch();

    /**
     * 通过视频订单ID获取被商家驳回的模特ID
     */
    Set<Long> selectRejectModelIdByVideoId(@Param("videoId") Long videoId);

    /**
     * 校验当前模特是否有其他的相同产品链接的订单
     */
    default boolean checkModelAnOrderWithTheSameProductLinkExists(Long modelId, String productLink) {
        return SpringUtils.getBean(IOrderVideoService.class).lambdaQuery()
                .eq(OrderVideo::getShootModelId, modelId)
                .eq(OrderVideo::getProductLink, productLink)
                .in(OrderVideo::getStatus, OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode(), OrderStatusEnum.NEED_CONFIRM.getCode(), OrderStatusEnum.FINISHED.getCode())
                .exists();
    }

    /**
     * 获取首次匹配成功率
     *
     * @return 首次匹配成功率
     */
    BigDecimal getSuccessRateOfTheFirstMatch();

    /**
     * 获取模特售后率
     *
     * @return 模特售后率
     */
    BigDecimal getModelAfterSalesRate();

    /**
     * 获取意向匹配成功率
     *
     * @return 意向匹配成功率
     */
    BigDecimal getSuccessRateOfIntentionMatching();

    /**
     * 获取模特超时率
     *
     * @return 模特超时率
     */
    BigDecimal getModelOvertimeRate();

    /**
     * 获取平均匹配时长（天）
     *
     * @return 平均匹配时长（天）
     */
    BigDecimal getAverageMatchingDuration(@Param("date") String date);

    /**
     * 获取平均反馈时长（天）
     *
     * @return 平均反馈时长（天）
     */
    BigDecimal getAverageFeedbackDuration();

    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    List<ModelOrderCommissionAnalysisBO> getModelOrderCommissionAnalysis(@Param("dto") ModelOrderCommissionAnalysisDTO dto);

    /**
     * 模特数据-成功匹配次数
     */
    ModelSuccessMatchCountVO getModelSuccessMatchCount();

    /**
     * 通过提交日期查询最新的匹配单
     */
    List<OrderVideoMatch> selectLastListBySubmitTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 通过提交日期查询提交过的匹配单
     */
    List<OrderVideoMatch> selectListBySubmitTime(@Param("beginDate") String beginDate, @Param("endDate") String endDate);

    /**
     * 我的预选-完成匹配-拍摄模特下拉框
     */
    default Set<Long> myPreselectShootModelSelect() {
        List<OrderVideoMatch> orderVideoMatches = selectList(new LambdaQueryWrapper<OrderVideoMatch>()
                .select(OrderVideoMatch::getShootModelId)
                .isNotNull(OrderVideoMatch::getShootModelId)
                .isNotNull(OrderVideoMatch::getSubmitTime)
        );

        return orderVideoMatches.stream().map(OrderVideoMatch::getShootModelId).collect(Collectors.toSet());
    }

    void updateShuffledSortKey(Long id);

    void refreshModelAllSort();

    /**
     * 查询模特数据表列表
     */
    List<ModelDataTableListVO> selectModelDataTableListByCondition(@Param("dto") ModelDataTableListDTO modelDataTableListDTO);

    /**
     * 模特数据表-售后率
     */
    List<ModelDataTableListVO> selectModelDataTableAfterSaleRate(@Param("dto") ModelDataTableListDTO modelDataTableListDTO);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表
     */
    List<MyPreselectDockingListVO> selectMyPreselectFailListByCondition(@Param("dto") MyPreselectFailListDTO dto);

    /**
     * 获取相同产品链接的订单
     */
    List<MyPreselectDockingListVO> getOrderTheSameProducts(@Param("productLinks") List<String> productLinks);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-排单推荐列表
     */
    List<MyPreselectFailListRecommendListVO> selectMyPreselectFailListRecommendList(
            @Param("modelId") Long modelId,
            @Param("platform") String platform,
            @Param("nation") Integer nation,
            @Param("modelType") Integer modelType,
            @Param("haveBlockedModelUserIds") List<Long> haveBlockedModelUserIds,
            @Param("productLink") String productLink,
            @Param("createOrderBusinessId") Long createOrderBusinessId,
            @Param("orderTagIds") List<Long> orderTagIds,
            @Param("modelTagIds") List<Long> modelTagIds,
            @Param("videoIds") List<Long> videoIds
    );

    /**
     * 通过视频订单ID获取视频订单最新的匹配单
     */
    List<OrderVideoMatch> selectLastOrderVideoMatchByVideoIds(@Param("videoIds") Collection<Long> videoIds);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-同链接订单
     */
    List<OrderTheSameProductListVO> selectOrderTheSameProductList(@Param("productLink") String productLink);

    /**
     * 获取同产品链接的模特ID
     */
    AddPreselectRemoteVO selectSameProductModelIdsByProductLink(@Param("productLink") String productLink);

    /**
     * 获取不满足凑单条件的模特ID
     */
    List<Long> getDoesNotMeetTheMinimumOrderRequirementModelIds(@Param("modelIds") List<Long> modelIds);
}
