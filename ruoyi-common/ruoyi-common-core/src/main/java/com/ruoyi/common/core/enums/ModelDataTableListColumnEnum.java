package com.ruoyi.common.core.enums;

import com.ruoyi.common.core.domain.dto.OrderByDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模特数据表列表列枚举
 *
 * <AUTHOR>
 * @date 2025/7/8 9:40
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ModelDataTableListColumnEnum {
    COLUMN1(1, "客服", "service_id", false),
    COLUMN2(2, "开发人", "developer_id", false),
    COLUMN3(3, "性别", "", false),
    COLUMN4(4, "国家", "", false),
    COLUMN5(5, "平台", "", false),
    COLUMN6(6, "类型", "type", false),
    COLUMN7(7, "年龄层", "age_group", true),
    COLUMN8(8, "等级", "cooperation_score", false),
    COLUMN9(9, "佣金", "commissionSort", false),
    COLUMN10(10, "案例数", "caseCount", false),
    COLUMN11(11, "标签数", "tagCount", false),
    COLUMN12(12, "品类数", "categoryCount", false),
    COLUMN13(13, "蜗牛照", "have_snail_pic", false),
    COLUMN14(14, "家庭", "familyMemberCount", false),
    COLUMN15(15, "收藏", "collectCount", false),
    COLUMN16(16, "拉黑", "blacklistCount", false),
    COLUMN17(17, "意向接单率", "intentionOrderRate", false),
    COLUMN18(18, "预选接单率", "preSelectOrderRate", false),
    COLUMN19(19, "分发接单率", "dispatchOrderRate", false),
    COLUMN20(20, "自选排单率", "selfSelectOrderRate", false),
    COLUMN21(21, "商家拒绝率", "rejectOrderRate", false),
    COLUMN22(22, "排单数", "orderScheduledCount", false),
    COLUMN23(23, "待拍数", "waitPictureCount", false),
    COLUMN24(24, "反馈数", "feedbackCount", false),
    COLUMN25(25, "超时率", "overtimeRate", false),
    COLUMN26(26, "售后率", "afterSaleRate", false),
    COLUMN27(27, "完成数", "completeCount", false),
    COLUMN28(28, "丢件数", "dropCount", false),
    COLUMN29(29, "被取消", "cancelCount", false),
    COLUMN30(30, "被退回", "returnCount", false),
    ;

    private Integer code;
    private String columnName;
    private String columnSortSqlName;
    private Boolean reversal;

    public static String getColumnSortSqlNameByCode(Integer code) {
        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            if (value.code.equals(code)) {
                return value.columnSortSqlName;
            }
        }
        return "";
    }

    public static String getColumnSortWayByCode(Integer code, String sortWay) {
        for (ModelDataTableListColumnEnum value : ModelDataTableListColumnEnum.values()) {
            if (value.code.equals(code)) {
                if (value.reversal) {
                    return OrderByDto.DIRECTION.DESC.value().equals(sortWay) ? OrderByDto.DIRECTION.ASC.value() : OrderByDto.DIRECTION.DESC.value();
                }
                return sortWay;
            }
        }
        return OrderByDto.DIRECTION.DESC.value();
    }

    public static List<Integer> getOrderColumns() {
        return List.of(
                COLUMN17.code,
                COLUMN18.code,
                COLUMN19.code,
                COLUMN20.code,
                COLUMN21.code,
                COLUMN22.code,
                COLUMN23.code,
                COLUMN24.code,
                COLUMN25.code,
                COLUMN26.code,
                COLUMN27.code,
                COLUMN28.code,
                COLUMN29.code,
                COLUMN30.code
        );
    }
}
