package com.ruoyi.common.core.constant;

/**
 * 订单相关常量
 */
public interface OrderConstant {
    /**
     * 订单编号前缀DD
     */
    String ORDER_NUM_PREFIX_DD = "DD";

    String ORDER_NUM_PREFIX_HY = "HY";

    /**
     * 合并订单支付单号前缀
     */
    String MERGE_ORDER_PAY_NUM_PREFIX_HBZF = "HBZF";

    /**
     * 普通订单支付单号前缀
     */
    String NORMAL_ORDER_PAY_NUM_PREFIX_ZFDD = "ZFDD";

    /**
     * 订单编号前缀WN
     */
    String ORDER_NUM_PREFIX_WN = "WN";
    /**
     * 退款编号前缀TK
     */
    String REFUND_NUM_PREFIX_WN = "TK";
    /**
     * 预付前缀: YF
     */
    String PREPAY_NUM = "YF";
    /**
     * 订单编号后缀8位随机数开始
     */
    Integer ORDER_NUM_RANDOM_MIN = 10000000;
    /**
     * 订单编号后缀8位随机数结束
     */
    Integer ORDER_NUM_RANDOM_MAX = 99999999;
    /**
     * 订单超时时间（天）
     */
    Integer ORDER_OVER_TIME = 1;
    /**
     * 订单购物车上限
     */
    Integer ORDER_CART_MAX = 200;
    /**
     * redis缓存的购物车key
     */
    String ORDER_CART_REDIS_KEY = "order_carts:";
    /**
     * 预选模特-超时未选择
     */
    String ORDER_PRESELECT_MODEL_OVERTIME_REMARK = "超时未选择";
    /**
     * 预选模特-模特不想要
     */
    String ORDER_PRESELECT_MODEL_UN_WISHED_REMARK = "模特不想要";
    /**
     * 预选模特-不满足订单要求
     */
    String ORDER_PRESELECT_MODEL_FAIL_TO_MEET_THE_CONDITION = "不满足订单要求";
    /**
     * 预选模特-模特已暂停合作
     */
    String ORDER_PRESELECT_MODEL_PAUSE_REMARK = "模特已暂停合作";
    /**
     * 预选模特-模特已取消合作
     */
    String ORDER_PRESELECT_MODEL_CANCEL_REMARK = "模特已取消合作";
    /**
     * 预选模特-模特接单上限了
     */
    String ORDER_PRESELECT_MODEL_ORDER_LIMIT_REMARK = "模特接单上限了";
    /**
     * 预选模特-模特有逾期订单
     */
    String ORDER_PRESELECT_MODEL_OVERDUE_REMARK = "模特有逾期订单";
    /**
     * 预选模特-模特被拉黑
     */
    String ORDER_PRESELECT_MODEL_BLACK_REMARK =  "模特拉黑";
    /**
     * 预选模特-模特行程中
     */
    String ORDER_PRESELECT_MODEL_ON_THE_ROAD = "行程中";
    /**
     * 预选模特-模特未展示
     */
    String ORDER_PRESELECT_MODEL_NOT_SHOWN = "模特已下架";
    /**
     * 预选模特-修改订单意向模特有变更
     */
    String ORDER_PRESELECT_MODEL_INTENTION_MODEL_CHANGE = "修改订单意向模特有变更";
    /**
     * 视频订单-商家催一催缓存key
     */
    String ORDER_VIDEO_REMINDER_KEY = "order_video_reminder:";

    String ORDER_VIDEO_OPERATE_PREFIX_EVENT_CONTENT = "#操作人#";

    /**
     * 新需求的操作人
     */
    String NEW_DEMAND_OPERATOR = "新需求的操作人";

    String ERROR_TIPS = "对不起，您没有操作权限";

    /**
     * 模特搜索内容条数上限
     */
    Integer MAX_SEARCH_COUNT = 30;

    /**
     * 订单回退
     */
    String ROLLBACK_ORDER = "订单回退";

    /**
     * 发票申票码前缀
     */
    String ORDER_INVOICE_CODE_PREFIX = "FP";


    /**
     * 类型-服务费
     */
    Integer TYPE_SERVICE_PRICE = 1;
}
