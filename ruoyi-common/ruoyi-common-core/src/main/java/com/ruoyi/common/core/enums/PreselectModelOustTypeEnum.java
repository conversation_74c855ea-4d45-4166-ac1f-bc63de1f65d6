package com.ruoyi.common.core.enums;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 意向模特淘汰类型
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum PreselectModelOustTypeEnum {

    MERCHANT_REJECTION(1, "商家驳回"),
    CUSTOMER_SERVICE_ELIMINATION(2, "客服淘汰"),
    WANT_NOT(3, "模特不想要"),
    NOT_SELECTED(4, "未被选中"),
    TIMEOUT_INTENTION_NOT_SELECTED(5, "超时未选择意向"),
    ROLLBACK_ORDER(6, "订单回退"),
    CHANGE_OF_COOPERATION_STATUS(7, "合作状态变更"),
    PAUSE_MATCHING(8, "暂停匹配"),
    TRANSACTION_CLOSED(9, "交易关闭"),
    ;
    private Integer code;
    private String label;

    public static String getChangeOfCooperationStatusRemark(Integer modelStatus) {
        if (ModelStatusEnum.PAUSE.getCode().equals(modelStatus)) {
            return "模特合作状态变更为" + ModelStatusEnum.PAUSE.getLabel();
        } else if (ModelStatusEnum.JOURNEY.getCode().equals(modelStatus)) {
            return "模特合作状态变更为" + ModelStatusEnum.JOURNEY.getLabel();
        } else if (ModelStatusEnum.CANCEL.getCode().equals(modelStatus)) {
            return "模特合作状态变更为" + ModelStatusEnum.CANCEL.getLabel();
        }
        return CharSequenceUtil.EMPTY;
    }
}
