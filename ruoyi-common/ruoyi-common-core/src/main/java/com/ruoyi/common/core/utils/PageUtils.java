package com.ruoyi.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.utils.sql.SqlUtil;
import com.ruoyi.common.core.web.page.PageDomain;
import com.ruoyi.common.core.web.page.TableSupport;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 分页工具类
 *
 * <AUTHOR>
 */
public class PageUtils extends PageHelper
{
    /**
     * 设置请求分页数据
     */
    public static void startPage() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = Optional.ofNullable(pageDomain.getPageNum()).orElse(1);
        Integer pageSize = Optional.ofNullable(pageDomain.getPageSize()).orElse(10);

        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
    }

    public static void startPageNoDefault() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
        Boolean reasonable = pageDomain.getReasonable();
        if (StringUtils.isNull(pageNum) || StringUtils.isNull(pageSize)){
            PageHelper.orderBy(orderBy);
        }else {
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }
    }

    public static void startPage(OrderByDto order) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        String orderBy = order.getSql();
        Boolean reasonable = pageDomain.getReasonable();
        if (StringUtils.isNull(pageNum) || StringUtils.isNull(pageSize)){
            PageHelper.orderBy(orderBy);
        }else {
            PageHelper.startPage(pageNum, pageSize, orderBy).setReasonable(reasonable);
        }

    }
    public static void orderBy(OrderByDto order) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        String orderBy = order.getSql();
        PageHelper.orderBy(orderBy);
    }

    /**
     * 清理分页的线程变量
     */
    public static void clearPage() {
        PageHelper.clearPage();
    }

    /**
     * 手动分页
     *
     * @param collection 数据集
     * @param <T>        泛型
     * @return 分页后的数据
     */
    public static <T> List<T> getPage(Collection<T> collection) {
        List<T> list = new ArrayList<>(collection);
        if (CollUtil.isEmpty(collection)) {
            return list;
        }
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNo = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, list.size());

        if (fromIndex > toIndex) {
            // 处理错误的页数情况，返回空列表或整个列表，或者抛出异常
            // 这里简单返回整个列表
            return list;
        }

        return list.subList(fromIndex, toIndex);
    }


    /**
     *
     * @param list              返回数据列表
     * @param totalList          计算总数列表
     * @return
     */
    public static <T> com.ruoyi.common.core.web.page.PageInfo<T> getDataTable(List<T> list, List<?> totalList) {
        com.ruoyi.common.core.web.page.PageInfo<T> rspData = new com.ruoyi.common.core.web.page.PageInfo<>();
        rspData.setTotal(new PageInfo(totalList).getTotal());
        rspData.setRows(list);
        return rspData;
    }
}
